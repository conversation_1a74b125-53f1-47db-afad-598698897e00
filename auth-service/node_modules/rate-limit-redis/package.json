{"name": "rate-limit-redis", "version": "4.2.2", "description": "A Redis store for the `express-rate-limit` middleware", "author": {"name": "<PERSON>", "url": "https://github.com/wyattjoh"}, "license": "MIT", "homepage": "https://github.com/express-rate-limit/rate-limit-redis", "repository": "https://github.com/express-rate-limit/rate-limit-redis", "type": "module", "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}}, "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["dist/", "tsconfig.json", "package.json", "readme.md", "license.md", "changelog.md"], "engines": {"node": ">= 16"}, "scripts": {"clean": "del-cli dist/ coverage/ *.log *.tmp *.tgz *.bak", "build": "pkgroll --src source/", "compile": "run-s clean build", "lint:code": "xo", "lint:rest": "prettier --check .", "lint": "run-s lint:*", "format:code": "xo --fix", "format:rest": "prettier --write .", "format": "run-s format:*", "test:lib": "jest", "test": "run-s lint test:*", "pre-commit": "lint-staged", "prepare": "run-s compile && husky install config/husky"}, "peerDependencies": {"express-rate-limit": ">= 6"}, "devDependencies": {"@express-rate-limit/prettier": "1.1.0", "@express-rate-limit/tsconfig": "1.0.0", "@jest/globals": "29.7.0", "@types/express": "4.17.17", "@types/ioredis-mock": "8.2.2", "@types/jest": "29.5.4", "@types/node": "20.6.1", "cross-env": "7.0.3", "del-cli": "5.1.0", "dts-bundle-generator": "8.0.1", "esbuild": "0.25.0", "express": "4.21.1", "express-rate-limit": "7.0.0", "husky": "8.0.3", "ioredis": "5.3.2", "ioredis-mock": "8.8.3", "jest": "29.7.0", "lint-staged": "14.0.1", "npm-run-all": "4.1.5", "pkgroll": "2.5.0", "prettier": "3.0.3", "serve": "14.2.1", "ts-jest": "29.1.1", "ts-node": "10.9.1", "typescript": "5.2.2", "xo": "0.56.0"}, "xo": {"prettier": true, "rules": {"@typescript-eslint/no-empty-function": 0, "@typescript-eslint/no-confusing-void-expression": 0, "@typescript-eslint/consistent-indexed-object-style": ["error", "index-signature"]}}, "prettier": "@express-rate-limit/prettier", "lint-staged": {"{source,test}/**/*.ts": "xo --fix", "*.{yaml,json,md}": "prettier --write"}}