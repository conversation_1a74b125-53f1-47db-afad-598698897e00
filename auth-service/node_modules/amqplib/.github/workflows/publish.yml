name: Publish

on:
  release:
    types: [created]

jobs:
  build:
    runs-on: ubuntu-latest
    services:
      rabbitmq:
        image: rabbitmq:3-alpine
        ports:
          - 5672:5672

    strategy:
      matrix:
        node-version: [10.x, 12.x, 14.x, 16.x, 18.x, 20.x]
        # See supported Node.js release schedule at https://nodejs.org/en/about/releases/

    steps:
      - uses: actions/checkout@v3

      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v3
        with:
          node-version: ${{ matrix.node-version }}
          cache: "npm"

      - run: npm ci

      - run: |
          n=0
          while :
          do
            sleep 5
            echo 'HELO\n\n\n\n' | nc localhost 5672 | grep AMQP
            [[ $? = 0 ]] && break || ((n++))
            (( n >= 5 )) && break
          done

      - run: echo 'HELO\n\n\n\n' | nc localhost 5672 | grep AMQP

      - run: make test

  publish:
    needs: build
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v4
        with:
          node-version: '20.x'
          cache: "npm"
          registry-url: https://registry.npmjs.org/
      - run: npm ci
      - run: npm publish --dry-run
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_AUTH_TOKEN }}
