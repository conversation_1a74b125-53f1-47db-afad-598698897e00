---
name: Bug report / Support request
about: Report a bug or ask for help
title: ''
labels: ''
assignees: ''

---

Before reporting a bug or requesting support please... 

1. Read the troubleshooting guide

   - https://amqp-node.github.io/amqplib/#troubleshooting

2. Search for existing open and closed issues

   - https://github.com/amqp-node/amqplib/issues?q=is%3Aissue+TYPE+YOUR+KEYWORDS+HERE

3. Ensure you are familiar with the amqplib Channel API and RabbitMQ basics

   - https://amqp-node.github.io/amqplib/channel_api.html
   - https://www.cloudamqp.com/blog/part1-rabbitmq-for-beginners-what-is-rabbitmq.html
   - https://www.rabbitmq.com/tutorials/amqp-concepts.html

If the above does not help, please provide as much of the following information as is relevant...

  - A clear and concise description of the problem
  - RabbitMQ version
  - amqplib version
  - Node.js version
  - The simplest possible code snippet that demonstrates the problem
  - A stack trace

Please format code snippets and/or stack traces using GitHub Markdown

  - https://docs.github.com/en/get-started/writing-on-github/working-with-advanced-formatting/creating-and-highlighting-code-blocks).

Thank you


