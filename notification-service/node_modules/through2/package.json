{"name": "through2", "version": "4.0.2", "description": "A tiny wrapper around Node.js streams.Transform (Streams2/3) to avoid explicit subclassing noise", "main": "through2.js", "scripts": {"test:node": "hundreds mocha test/test.js", "test:browser": "node -e 'process.exit(process.version.startsWith(\"v8.\") ? 0 : 1)' || polendina --cleanup --runner=mocha test/test.js", "test": "npm run lint && npm run test:node && npm run test:browser", "lint": "standard", "coverage": "c8 --reporter=text --reporter=html mocha test/test.js && npx st -d coverage -p 8888"}, "repository": {"type": "git", "url": "https://github.com/rvagg/through2.git"}, "keywords": ["stream", "streams2", "through", "transform"], "author": "<PERSON>g <<EMAIL>> (https://github.com/rvagg)", "license": "MIT", "dependencies": {"readable-stream": "3"}, "devDependencies": {"bl": "^4.0.2", "buffer": "^5.6.0", "chai": "^4.2.0", "hundreds": "~0.0.7", "mocha": "^7.2.0", "polendina": "^1.0.0", "standard": "^14.3.4", "stream-spigot": "^3.0.6"}}