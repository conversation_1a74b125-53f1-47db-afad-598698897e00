{"version": "0.2.0", "configurations": [{"type": "node", "request": "launch", "name": "Jasmine Tests", "program": "${workspaceFolder}/node_modules/jasmine/bin/jasmine.js", "args": ["${workspaceFolder}/spec/attr_spec.js"], "internalConsoleOptions": "openOnSessionStart"}, {"type": "node", "request": "launch", "name": "Jasmine Tests current test file", "program": "${workspaceFolder}/node_modules/jasmine/bin/jasmine.js", "args": ["${file}"], "internalConsoleOptions": "openOnSessionStart"}]}