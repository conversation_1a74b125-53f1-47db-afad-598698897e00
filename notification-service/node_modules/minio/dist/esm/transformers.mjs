/*
 * MinIO Javascript Library for Amazon S3 Compatible Cloud Storage, (C) 2015, 2016 MinIO, Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import * as Crypto from "crypto";
import Through2 from 'through2';
import { isFunction } from "./internal/helper.mjs";
import * as xmlParsers from "./xml-parsers.mjs";

// getConcater returns a stream that concatenates the input and emits
// the concatenated output when 'end' has reached. If an optional
// parser function is passed upon reaching the 'end' of the stream,
// `parser(concatenated_data)` will be emitted.
export function getConcater(parser, emitError) {
  var objectMode = false;
  var bufs = [];
  if (parser && !isFunction(parser)) {
    throw new TypeError('parser should be of type "function"');
  }
  if (parser) {
    objectMode = true;
  }
  return Through2({
    objectMode
  }, function (chunk, enc, cb) {
    bufs.push(chunk);
    cb();
  }, function (cb) {
    if (emitError) {
      cb(parser(Buffer.concat(bufs).toString()));
      // cb(e) would mean we have to emit 'end' by explicitly calling this.push(null)
      this.push(null);
      return;
    }
    if (bufs.length) {
      if (parser) {
        this.push(parser(Buffer.concat(bufs).toString()));
      } else {
        this.push(Buffer.concat(bufs));
      }
    }
    cb();
  });
}

// A through stream that calculates md5sum and sha256sum
export function getHashSummer(enableSHA256) {
  var md5 = Crypto.createHash('md5');
  var sha256 = Crypto.createHash('sha256');
  return Through2.obj(function (chunk, enc, cb) {
    if (enableSHA256) {
      sha256.update(chunk);
    } else {
      md5.update(chunk);
    }
    cb();
  }, function (cb) {
    var md5sum = '';
    var sha256sum = '';
    if (enableSHA256) {
      sha256sum = sha256.digest('hex');
    } else {
      md5sum = md5.digest('base64');
    }
    var hashData = {
      md5sum,
      sha256sum
    };
    this.push(hashData);
    this.push(null);
    cb();
  });
}

// Following functions return a stream object that parses XML
// and emits suitable Javascript objects.

// Parses listObjects response.
export function getListObjectsV2Transformer() {
  return getConcater(xmlParsers.parseListObjectsV2);
}

// Parses listObjects with metadata response.
export function getListObjectsV2WithMetadataTransformer() {
  return getConcater(xmlParsers.parseListObjectsV2WithMetadata);
}

// Parses GET/SET BucketNotification response
export function getBucketNotificationTransformer() {
  return getConcater(xmlParsers.parseBucketNotification);
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJDcnlwdG8iLCJUaHJvdWdoMiIsImlzRnVuY3Rpb24iLCJ4bWxQYXJzZXJzIiwiZ2V0Q29uY2F0ZXIiLCJwYXJzZXIiLCJlbWl0RXJyb3IiLCJvYmplY3RNb2RlIiwiYnVmcyIsIlR5cGVFcnJvciIsImNodW5rIiwiZW5jIiwiY2IiLCJwdXNoIiwiQnVmZmVyIiwiY29uY2F0IiwidG9TdHJpbmciLCJsZW5ndGgiLCJnZXRIYXNoU3VtbWVyIiwiZW5hYmxlU0hBMjU2IiwibWQ1IiwiY3JlYXRlSGFzaCIsInNoYTI1NiIsIm9iaiIsInVwZGF0ZSIsIm1kNXN1bSIsInNoYTI1NnN1bSIsImRpZ2VzdCIsImhhc2hEYXRhIiwiZ2V0TGlzdE9iamVjdHNWMlRyYW5zZm9ybWVyIiwicGFyc2VMaXN0T2JqZWN0c1YyIiwiZ2V0TGlzdE9iamVjdHNWMldpdGhNZXRhZGF0YVRyYW5zZm9ybWVyIiwicGFyc2VMaXN0T2JqZWN0c1YyV2l0aE1ldGFkYXRhIiwiZ2V0QnVja2V0Tm90aWZpY2F0aW9uVHJhbnNmb3JtZXIiLCJwYXJzZUJ1Y2tldE5vdGlmaWNhdGlvbiJdLCJzb3VyY2VzIjpbInRyYW5zZm9ybWVycy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKlxuICogTWluSU8gSmF2YXNjcmlwdCBMaWJyYXJ5IGZvciBBbWF6b24gUzMgQ29tcGF0aWJsZSBDbG91ZCBTdG9yYWdlLCAoQykgMjAxNSwgMjAxNiBNaW5JTywgSW5jLlxuICpcbiAqIExpY2Vuc2VkIHVuZGVyIHRoZSBBcGFjaGUgTGljZW5zZSwgVmVyc2lvbiAyLjAgKHRoZSBcIkxpY2Vuc2VcIik7XG4gKiB5b3UgbWF5IG5vdCB1c2UgdGhpcyBmaWxlIGV4Y2VwdCBpbiBjb21wbGlhbmNlIHdpdGggdGhlIExpY2Vuc2UuXG4gKiBZb3UgbWF5IG9idGFpbiBhIGNvcHkgb2YgdGhlIExpY2Vuc2UgYXRcbiAqXG4gKiAgICAgaHR0cDovL3d3dy5hcGFjaGUub3JnL2xpY2Vuc2VzL0xJQ0VOU0UtMi4wXG4gKlxuICogVW5sZXNzIHJlcXVpcmVkIGJ5IGFwcGxpY2FibGUgbGF3IG9yIGFncmVlZCB0byBpbiB3cml0aW5nLCBzb2Z0d2FyZVxuICogZGlzdHJpYnV0ZWQgdW5kZXIgdGhlIExpY2Vuc2UgaXMgZGlzdHJpYnV0ZWQgb24gYW4gXCJBUyBJU1wiIEJBU0lTLFxuICogV0lUSE9VVCBXQVJSQU5USUVTIE9SIENPTkRJVElPTlMgT0YgQU5ZIEtJTkQsIGVpdGhlciBleHByZXNzIG9yIGltcGxpZWQuXG4gKiBTZWUgdGhlIExpY2Vuc2UgZm9yIHRoZSBzcGVjaWZpYyBsYW5ndWFnZSBnb3Zlcm5pbmcgcGVybWlzc2lvbnMgYW5kXG4gKiBsaW1pdGF0aW9ucyB1bmRlciB0aGUgTGljZW5zZS5cbiAqL1xuXG5pbXBvcnQgKiBhcyBDcnlwdG8gZnJvbSAnbm9kZTpjcnlwdG8nXG5cbmltcG9ydCBUaHJvdWdoMiBmcm9tICd0aHJvdWdoMidcblxuaW1wb3J0IHsgaXNGdW5jdGlvbiB9IGZyb20gJy4vaW50ZXJuYWwvaGVscGVyLnRzJ1xuaW1wb3J0ICogYXMgeG1sUGFyc2VycyBmcm9tICcuL3htbC1wYXJzZXJzLmpzJ1xuXG4vLyBnZXRDb25jYXRlciByZXR1cm5zIGEgc3RyZWFtIHRoYXQgY29uY2F0ZW5hdGVzIHRoZSBpbnB1dCBhbmQgZW1pdHNcbi8vIHRoZSBjb25jYXRlbmF0ZWQgb3V0cHV0IHdoZW4gJ2VuZCcgaGFzIHJlYWNoZWQuIElmIGFuIG9wdGlvbmFsXG4vLyBwYXJzZXIgZnVuY3Rpb24gaXMgcGFzc2VkIHVwb24gcmVhY2hpbmcgdGhlICdlbmQnIG9mIHRoZSBzdHJlYW0sXG4vLyBgcGFyc2VyKGNvbmNhdGVuYXRlZF9kYXRhKWAgd2lsbCBiZSBlbWl0dGVkLlxuZXhwb3J0IGZ1bmN0aW9uIGdldENvbmNhdGVyKHBhcnNlciwgZW1pdEVycm9yKSB7XG4gIHZhciBvYmplY3RNb2RlID0gZmFsc2VcbiAgdmFyIGJ1ZnMgPSBbXVxuXG4gIGlmIChwYXJzZXIgJiYgIWlzRnVuY3Rpb24ocGFyc2VyKSkge1xuICAgIHRocm93IG5ldyBUeXBlRXJyb3IoJ3BhcnNlciBzaG91bGQgYmUgb2YgdHlwZSBcImZ1bmN0aW9uXCInKVxuICB9XG5cbiAgaWYgKHBhcnNlcikge1xuICAgIG9iamVjdE1vZGUgPSB0cnVlXG4gIH1cblxuICByZXR1cm4gVGhyb3VnaDIoXG4gICAgeyBvYmplY3RNb2RlIH0sXG4gICAgZnVuY3Rpb24gKGNodW5rLCBlbmMsIGNiKSB7XG4gICAgICBidWZzLnB1c2goY2h1bmspXG4gICAgICBjYigpXG4gICAgfSxcbiAgICBmdW5jdGlvbiAoY2IpIHtcbiAgICAgIGlmIChlbWl0RXJyb3IpIHtcbiAgICAgICAgY2IocGFyc2VyKEJ1ZmZlci5jb25jYXQoYnVmcykudG9TdHJpbmcoKSkpXG4gICAgICAgIC8vIGNiKGUpIHdvdWxkIG1lYW4gd2UgaGF2ZSB0byBlbWl0ICdlbmQnIGJ5IGV4cGxpY2l0bHkgY2FsbGluZyB0aGlzLnB1c2gobnVsbClcbiAgICAgICAgdGhpcy5wdXNoKG51bGwpXG4gICAgICAgIHJldHVyblxuICAgICAgfVxuICAgICAgaWYgKGJ1ZnMubGVuZ3RoKSB7XG4gICAgICAgIGlmIChwYXJzZXIpIHtcbiAgICAgICAgICB0aGlzLnB1c2gocGFyc2VyKEJ1ZmZlci5jb25jYXQoYnVmcykudG9TdHJpbmcoKSkpXG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgdGhpcy5wdXNoKEJ1ZmZlci5jb25jYXQoYnVmcykpXG4gICAgICAgIH1cbiAgICAgIH1cbiAgICAgIGNiKClcbiAgICB9LFxuICApXG59XG5cbi8vIEEgdGhyb3VnaCBzdHJlYW0gdGhhdCBjYWxjdWxhdGVzIG1kNXN1bSBhbmQgc2hhMjU2c3VtXG5leHBvcnQgZnVuY3Rpb24gZ2V0SGFzaFN1bW1lcihlbmFibGVTSEEyNTYpIHtcbiAgdmFyIG1kNSA9IENyeXB0by5jcmVhdGVIYXNoKCdtZDUnKVxuICB2YXIgc2hhMjU2ID0gQ3J5cHRvLmNyZWF0ZUhhc2goJ3NoYTI1NicpXG5cbiAgcmV0dXJuIFRocm91Z2gyLm9iaihcbiAgICBmdW5jdGlvbiAoY2h1bmssIGVuYywgY2IpIHtcbiAgICAgIGlmIChlbmFibGVTSEEyNTYpIHtcbiAgICAgICAgc2hhMjU2LnVwZGF0ZShjaHVuaylcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIG1kNS51cGRhdGUoY2h1bmspXG4gICAgICB9XG4gICAgICBjYigpXG4gICAgfSxcbiAgICBmdW5jdGlvbiAoY2IpIHtcbiAgICAgIHZhciBtZDVzdW0gPSAnJ1xuICAgICAgdmFyIHNoYTI1NnN1bSA9ICcnXG4gICAgICBpZiAoZW5hYmxlU0hBMjU2KSB7XG4gICAgICAgIHNoYTI1NnN1bSA9IHNoYTI1Ni5kaWdlc3QoJ2hleCcpXG4gICAgICB9IGVsc2Uge1xuICAgICAgICBtZDVzdW0gPSBtZDUuZGlnZXN0KCdiYXNlNjQnKVxuICAgICAgfVxuICAgICAgdmFyIGhhc2hEYXRhID0geyBtZDVzdW0sIHNoYTI1NnN1bSB9XG4gICAgICB0aGlzLnB1c2goaGFzaERhdGEpXG4gICAgICB0aGlzLnB1c2gobnVsbClcbiAgICAgIGNiKClcbiAgICB9LFxuICApXG59XG5cbi8vIEZvbGxvd2luZyBmdW5jdGlvbnMgcmV0dXJuIGEgc3RyZWFtIG9iamVjdCB0aGF0IHBhcnNlcyBYTUxcbi8vIGFuZCBlbWl0cyBzdWl0YWJsZSBKYXZhc2NyaXB0IG9iamVjdHMuXG5cbi8vIFBhcnNlcyBsaXN0T2JqZWN0cyByZXNwb25zZS5cbmV4cG9ydCBmdW5jdGlvbiBnZXRMaXN0T2JqZWN0c1YyVHJhbnNmb3JtZXIoKSB7XG4gIHJldHVybiBnZXRDb25jYXRlcih4bWxQYXJzZXJzLnBhcnNlTGlzdE9iamVjdHNWMilcbn1cblxuLy8gUGFyc2VzIGxpc3RPYmplY3RzIHdpdGggbWV0YWRhdGEgcmVzcG9uc2UuXG5leHBvcnQgZnVuY3Rpb24gZ2V0TGlzdE9iamVjdHNWMldpdGhNZXRhZGF0YVRyYW5zZm9ybWVyKCkge1xuICByZXR1cm4gZ2V0Q29uY2F0ZXIoeG1sUGFyc2Vycy5wYXJzZUxpc3RPYmplY3RzVjJXaXRoTWV0YWRhdGEpXG59XG5cbi8vIFBhcnNlcyBHRVQvU0VUIEJ1Y2tldE5vdGlmaWNhdGlvbiByZXNwb25zZVxuZXhwb3J0IGZ1bmN0aW9uIGdldEJ1Y2tldE5vdGlmaWNhdGlvblRyYW5zZm9ybWVyKCkge1xuICByZXR1cm4gZ2V0Q29uY2F0ZXIoeG1sUGFyc2Vycy5wYXJzZUJ1Y2tldE5vdGlmaWNhdGlvbilcbn1cbiJdLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLE9BQU8sS0FBS0EsTUFBTTtBQUVsQixPQUFPQyxRQUFRLE1BQU0sVUFBVTtBQUUvQixTQUFTQyxVQUFVLFFBQVEsdUJBQXNCO0FBQ2pELE9BQU8sS0FBS0MsVUFBVSxNQUFNLG1CQUFrQjs7QUFFOUM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPLFNBQVNDLFdBQVdBLENBQUNDLE1BQU0sRUFBRUMsU0FBUyxFQUFFO0VBQzdDLElBQUlDLFVBQVUsR0FBRyxLQUFLO0VBQ3RCLElBQUlDLElBQUksR0FBRyxFQUFFO0VBRWIsSUFBSUgsTUFBTSxJQUFJLENBQUNILFVBQVUsQ0FBQ0csTUFBTSxDQUFDLEVBQUU7SUFDakMsTUFBTSxJQUFJSSxTQUFTLENBQUMscUNBQXFDLENBQUM7RUFDNUQ7RUFFQSxJQUFJSixNQUFNLEVBQUU7SUFDVkUsVUFBVSxHQUFHLElBQUk7RUFDbkI7RUFFQSxPQUFPTixRQUFRLENBQ2I7SUFBRU07RUFBVyxDQUFDLEVBQ2QsVUFBVUcsS0FBSyxFQUFFQyxHQUFHLEVBQUVDLEVBQUUsRUFBRTtJQUN4QkosSUFBSSxDQUFDSyxJQUFJLENBQUNILEtBQUssQ0FBQztJQUNoQkUsRUFBRSxDQUFDLENBQUM7RUFDTixDQUFDLEVBQ0QsVUFBVUEsRUFBRSxFQUFFO0lBQ1osSUFBSU4sU0FBUyxFQUFFO01BQ2JNLEVBQUUsQ0FBQ1AsTUFBTSxDQUFDUyxNQUFNLENBQUNDLE1BQU0sQ0FBQ1AsSUFBSSxDQUFDLENBQUNRLFFBQVEsQ0FBQyxDQUFDLENBQUMsQ0FBQztNQUMxQztNQUNBLElBQUksQ0FBQ0gsSUFBSSxDQUFDLElBQUksQ0FBQztNQUNmO0lBQ0Y7SUFDQSxJQUFJTCxJQUFJLENBQUNTLE1BQU0sRUFBRTtNQUNmLElBQUlaLE1BQU0sRUFBRTtRQUNWLElBQUksQ0FBQ1EsSUFBSSxDQUFDUixNQUFNLENBQUNTLE1BQU0sQ0FBQ0MsTUFBTSxDQUFDUCxJQUFJLENBQUMsQ0FBQ1EsUUFBUSxDQUFDLENBQUMsQ0FBQyxDQUFDO01BQ25ELENBQUMsTUFBTTtRQUNMLElBQUksQ0FBQ0gsSUFBSSxDQUFDQyxNQUFNLENBQUNDLE1BQU0sQ0FBQ1AsSUFBSSxDQUFDLENBQUM7TUFDaEM7SUFDRjtJQUNBSSxFQUFFLENBQUMsQ0FBQztFQUNOLENBQ0YsQ0FBQztBQUNIOztBQUVBO0FBQ0EsT0FBTyxTQUFTTSxhQUFhQSxDQUFDQyxZQUFZLEVBQUU7RUFDMUMsSUFBSUMsR0FBRyxHQUFHcEIsTUFBTSxDQUFDcUIsVUFBVSxDQUFDLEtBQUssQ0FBQztFQUNsQyxJQUFJQyxNQUFNLEdBQUd0QixNQUFNLENBQUNxQixVQUFVLENBQUMsUUFBUSxDQUFDO0VBRXhDLE9BQU9wQixRQUFRLENBQUNzQixHQUFHLENBQ2pCLFVBQVViLEtBQUssRUFBRUMsR0FBRyxFQUFFQyxFQUFFLEVBQUU7SUFDeEIsSUFBSU8sWUFBWSxFQUFFO01BQ2hCRyxNQUFNLENBQUNFLE1BQU0sQ0FBQ2QsS0FBSyxDQUFDO0lBQ3RCLENBQUMsTUFBTTtNQUNMVSxHQUFHLENBQUNJLE1BQU0sQ0FBQ2QsS0FBSyxDQUFDO0lBQ25CO0lBQ0FFLEVBQUUsQ0FBQyxDQUFDO0VBQ04sQ0FBQyxFQUNELFVBQVVBLEVBQUUsRUFBRTtJQUNaLElBQUlhLE1BQU0sR0FBRyxFQUFFO0lBQ2YsSUFBSUMsU0FBUyxHQUFHLEVBQUU7SUFDbEIsSUFBSVAsWUFBWSxFQUFFO01BQ2hCTyxTQUFTLEdBQUdKLE1BQU0sQ0FBQ0ssTUFBTSxDQUFDLEtBQUssQ0FBQztJQUNsQyxDQUFDLE1BQU07TUFDTEYsTUFBTSxHQUFHTCxHQUFHLENBQUNPLE1BQU0sQ0FBQyxRQUFRLENBQUM7SUFDL0I7SUFDQSxJQUFJQyxRQUFRLEdBQUc7TUFBRUgsTUFBTTtNQUFFQztJQUFVLENBQUM7SUFDcEMsSUFBSSxDQUFDYixJQUFJLENBQUNlLFFBQVEsQ0FBQztJQUNuQixJQUFJLENBQUNmLElBQUksQ0FBQyxJQUFJLENBQUM7SUFDZkQsRUFBRSxDQUFDLENBQUM7RUFDTixDQUNGLENBQUM7QUFDSDs7QUFFQTtBQUNBOztBQUVBO0FBQ0EsT0FBTyxTQUFTaUIsMkJBQTJCQSxDQUFBLEVBQUc7RUFDNUMsT0FBT3pCLFdBQVcsQ0FBQ0QsVUFBVSxDQUFDMkIsa0JBQWtCLENBQUM7QUFDbkQ7O0FBRUE7QUFDQSxPQUFPLFNBQVNDLHVDQUF1Q0EsQ0FBQSxFQUFHO0VBQ3hELE9BQU8zQixXQUFXLENBQUNELFVBQVUsQ0FBQzZCLDhCQUE4QixDQUFDO0FBQy9EOztBQUVBO0FBQ0EsT0FBTyxTQUFTQyxnQ0FBZ0NBLENBQUEsRUFBRztFQUNqRCxPQUFPN0IsV0FBVyxDQUFDRCxVQUFVLENBQUMrQix1QkFBdUIsQ0FBQztBQUN4RCJ9