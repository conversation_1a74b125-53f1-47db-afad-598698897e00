// Build PostPolicy object that can be signed by presignedPostPolicy
import * as errors from "../errors.mjs";
import { isObject, isValidBucketName, isValidObjectName, isValidPrefix } from "./helper.mjs";
export class PostPolicy {
  policy = {
    conditions: []
  };
  formData = {};

  // set expiration date
  setExpires(date) {
    if (!date) {
      throw new errors.InvalidDateError('Invalid date: cannot be null');
    }
    this.policy.expiration = date.toISOString();
  }

  // set object name
  setKey(objectName) {
    if (!isValidObjectName(objectName)) {
      throw new errors.InvalidObjectNameError(`Invalid object name : ${objectName}`);
    }
    this.policy.conditions.push(['eq', '$key', objectName]);
    this.formData.key = objectName;
  }

  // set object name prefix, i.e policy allows any keys with this prefix
  setKeyStartsWith(prefix) {
    if (!isValidPrefix(prefix)) {
      throw new errors.InvalidPrefixError(`Invalid prefix : ${prefix}`);
    }
    this.policy.conditions.push(['starts-with', '$key', prefix]);
    this.formData.key = prefix;
  }

  // set bucket name
  setBucket(bucketName) {
    if (!isValidBucketName(bucketName)) {
      throw new errors.InvalidBucketNameError(`Invalid bucket name : ${bucketName}`);
    }
    this.policy.conditions.push(['eq', '$bucket', bucketName]);
    this.formData.bucket = bucketName;
  }

  // set Content-Type
  setContentType(type) {
    if (!type) {
      throw new Error('content-type cannot be null');
    }
    this.policy.conditions.push(['eq', '$Content-Type', type]);
    this.formData['Content-Type'] = type;
  }

  // set Content-Type prefix, i.e image/ allows any image
  setContentTypeStartsWith(prefix) {
    if (!prefix) {
      throw new Error('content-type cannot be null');
    }
    this.policy.conditions.push(['starts-with', '$Content-Type', prefix]);
    this.formData['Content-Type'] = prefix;
  }

  // set Content-Disposition
  setContentDisposition(value) {
    if (!value) {
      throw new Error('content-disposition cannot be null');
    }
    this.policy.conditions.push(['eq', '$Content-Disposition', value]);
    this.formData['Content-Disposition'] = value;
  }

  // set minimum/maximum length of what Content-Length can be.
  setContentLengthRange(min, max) {
    if (min > max) {
      throw new Error('min cannot be more than max');
    }
    if (min < 0) {
      throw new Error('min should be > 0');
    }
    if (max < 0) {
      throw new Error('max should be > 0');
    }
    this.policy.conditions.push(['content-length-range', min, max]);
  }

  // set user defined metadata
  setUserMetaData(metaData) {
    if (!isObject(metaData)) {
      throw new TypeError('metadata should be of type "object"');
    }
    Object.entries(metaData).forEach(([key, value]) => {
      const amzMetaDataKey = `x-amz-meta-${key}`;
      this.policy.conditions.push(['eq', `$${amzMetaDataKey}`, value]);
      this.formData[amzMetaDataKey] = value.toString();
    });
  }
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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