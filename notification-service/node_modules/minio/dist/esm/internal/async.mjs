// promise helper for stdlib

import * as fs from "fs";
import * as stream from "stream";
import { promisify } from "util";

// TODO: use "node:fs/promise" directly after we stop testing on nodejs 12
export { promises as fsp } from 'node:fs';
export const streamPromise = {
  // node:stream/promises Added in: v15.0.0
  pipeline: promisify(stream.pipeline)
};
export const fstat = promisify(fs.fstat);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJmcyIsInN0cmVhbSIsInByb21pc2lmeSIsInByb21pc2VzIiwiZnNwIiwic3RyZWFtUHJvbWlzZSIsInBpcGVsaW5lIiwiZnN0YXQiXSwic291cmNlcyI6WyJhc3luYy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBwcm9taXNlIGhlbHBlciBmb3Igc3RkbGliXG5cbmltcG9ydCAqIGFzIGZzIGZyb20gJ25vZGU6ZnMnXG5pbXBvcnQgKiBhcyBzdHJlYW0gZnJvbSAnbm9kZTpzdHJlYW0nXG5pbXBvcnQgeyBwcm9taXNpZnkgfSBmcm9tICdub2RlOnV0aWwnXG5cbi8vIFRPRE86IHVzZSBcIm5vZGU6ZnMvcHJvbWlzZVwiIGRpcmVjdGx5IGFmdGVyIHdlIHN0b3AgdGVzdGluZyBvbiBub2RlanMgMTJcbmV4cG9ydCB7IHByb21pc2VzIGFzIGZzcCB9IGZyb20gJ25vZGU6ZnMnXG5leHBvcnQgY29uc3Qgc3RyZWFtUHJvbWlzZSA9IHtcbiAgLy8gbm9kZTpzdHJlYW0vcHJvbWlzZXMgQWRkZWQgaW46IHYxNS4wLjBcbiAgcGlwZWxpbmU6IHByb21pc2lmeShzdHJlYW0ucGlwZWxpbmUpLFxufVxuXG5leHBvcnQgY29uc3QgZnN0YXQgPSBwcm9taXNpZnkoZnMuZnN0YXQpXG4iXSwibWFwcGluZ3MiOiJBQUFBOztBQUVBLE9BQU8sS0FBS0EsRUFBRTtBQUNkLE9BQU8sS0FBS0MsTUFBTTtBQUNsQixTQUFTQyxTQUFTOztBQUVsQjtBQUNBLFNBQVNDLFFBQVEsSUFBSUMsR0FBRyxRQUFRLFNBQVM7QUFDekMsT0FBTyxNQUFNQyxhQUFhLEdBQUc7RUFDM0I7RUFDQUMsUUFBUSxFQUFFSixTQUFTLENBQUNELE1BQU0sQ0FBQ0ssUUFBUTtBQUNyQyxDQUFDO0FBRUQsT0FBTyxNQUFNQyxLQUFLLEdBQUdMLFNBQVMsQ0FBQ0YsRUFBRSxDQUFDTyxLQUFLLENBQUMifQ==