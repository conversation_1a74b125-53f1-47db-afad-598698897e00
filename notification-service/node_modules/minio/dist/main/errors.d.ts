/// <reference lib="es2022.error" />
declare class ExtendableError extends Error {
  constructor(message?: string, opt?: ErrorOptions);
}
/**
 * AnonymousRequestError is generated for anonymous keys on specific
 * APIs. NOTE: PresignedURL generation always requires access keys.
 */
export declare class AnonymousRequestError extends ExtendableError {}
/**
 * InvalidArgumentError is generated for all invalid arguments.
 */
export declare class InvalidArgumentError extends ExtendableError {}
/**
 * InvalidPortError is generated when a non integer value is provided
 * for ports.
 */
export declare class InvalidPortError extends ExtendableError {}
/**
 * InvalidEndpointError is generated when an invalid end point value is
 * provided which does not follow domain standards.
 */
export declare class InvalidEndpointError extends ExtendableError {}
/**
 * InvalidBucketNameError is generated when an invalid bucket name is
 * provided which does not follow AWS S3 specifications.
 * http://docs.aws.amazon.com/AmazonS3/latest/dev/BucketRestrictions.html
 */
export declare class InvalidBucketNameError extends ExtendableError {}
/**
 * InvalidObjectNameError is generated when an invalid object name is
 * provided which does not follow AWS S3 specifications.
 * http://docs.aws.amazon.com/AmazonS3/latest/dev/UsingMetadata.html
 */
export declare class InvalidObjectNameError extends ExtendableError {}
/**
 * AccessKeyRequiredError generated by signature methods when access
 * key is not found.
 */
export declare class AccessKeyRequiredError extends ExtendableError {}
/**
 * SecretKeyRequiredError generated by signature methods when secret
 * key is not found.
 */
export declare class SecretKeyRequiredError extends ExtendableError {}
/**
 * ExpiresParamError generated when expires parameter value is not
 * well within stipulated limits.
 */
export declare class ExpiresParamError extends ExtendableError {}
/**
 * InvalidDateError generated when invalid date is found.
 */
export declare class InvalidDateError extends ExtendableError {}
/**
 * InvalidPrefixError generated when object prefix provided is invalid
 * or does not conform to AWS S3 object key restrictions.
 */
export declare class InvalidPrefixError extends ExtendableError {}
/**
 * InvalidBucketPolicyError generated when the given bucket policy is invalid.
 */
export declare class InvalidBucketPolicyError extends ExtendableError {}
/**
 * IncorrectSizeError generated when total data read mismatches with
 * the input size.
 */
export declare class IncorrectSizeError extends ExtendableError {}
/**
 * InvalidXMLError generated when an unknown XML is found.
 */
export declare class InvalidXMLError extends ExtendableError {}
/**
 * S3Error is generated for errors returned from S3 server.
 * see getErrorTransformer for details
 */
export declare class S3Error extends ExtendableError {
  code?: string;
  region?: string;
}
export declare class IsValidBucketNameError extends ExtendableError {}
export {};