"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.removeDirAndFiles = removeDirAndFiles;
var fs = _interopRequireWildcard(require("fs"), true);
var path = _interopRequireWildcard(require("path"), true);
var querystring = _interopRequireWildcard(require("query-string"), true);
var errors = _interopRequireWildcard(require("./errors.js"), true);
var _helper = require("./internal/helper.js");
var _type = require("./internal/type.js");
exports.RETENTION_MODES = _type.RETENTION_MODES;
exports.ENCRYPTION_TYPES = _type.ENCRYPTION_TYPES;
exports.LEGAL_HOLD_STATUS = _type.LEGAL_HOLD_STATUS;
exports.RETENTION_VALIDITY_UNITS = _type.RETENTION_VALIDITY_UNITS;
function _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== "function") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function (nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }
function _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || typeof obj !== "object" && typeof obj !== "function") { return { default: obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }
const DEFAULT_REGION = 'us-east-1';
exports.DEFAULT_REGION = DEFAULT_REGION;
const PRESIGN_EXPIRY_DAYS_MAX = 24 * 60 * 60 * 7; // 7 days in seconds
exports.PRESIGN_EXPIRY_DAYS_MAX = PRESIGN_EXPIRY_DAYS_MAX;
class CopySourceOptions {
  constructor({
    Bucket,
    Object,
    VersionID = '',
    MatchETag = '',
    NoMatchETag = '',
    MatchModifiedSince = null,
    MatchUnmodifiedSince = null,
    MatchRange = false,
    Start = 0,
    End = 0,
    Encryption = undefined
  }) {
    this.Bucket = Bucket;
    this.Object = Object;
    this.VersionID = VersionID;
    this.MatchETag = MatchETag;
    this.NoMatchETag = NoMatchETag;
    this.MatchModifiedSince = MatchModifiedSince;
    this.MatchUnmodifiedSince = MatchUnmodifiedSince;
    this.MatchRange = MatchRange;
    this.Start = Start;
    this.End = End;
    this.Encryption = Encryption;
  }
  validate() {
    if (!(0, _helper.isValidBucketName)(this.Bucket)) {
      throw new errors.InvalidBucketNameError('Invalid Source bucket name: ' + this.Bucket);
    }
    if (!(0, _helper.isValidObjectName)(this.Object)) {
      throw new errors.InvalidObjectNameError(`Invalid Source object name: ${this.Object}`);
    }
    if (this.MatchRange && this.Start !== -1 && this.End !== -1 && this.Start > this.End || this.Start < 0) {
      throw new errors.InvalidObjectNameError('Source start must be non-negative, and start must be at most end.');
    } else if (this.MatchRange && !(0, _helper.isNumber)(this.Start) || !(0, _helper.isNumber)(this.End)) {
      throw new errors.InvalidObjectNameError('MatchRange is specified. But Invalid Start and End values are specified.');
    }
    return true;
  }
  getHeaders() {
    const headerOptions = {};
    headerOptions['x-amz-copy-source'] = encodeURI(this.Bucket + '/' + this.Object);
    if (!(0, _helper.isEmpty)(this.VersionID)) {
      headerOptions['x-amz-copy-source'] = `${encodeURI(this.Bucket + '/' + this.Object)}?versionId=${this.VersionID}`;
    }
    if (!(0, _helper.isEmpty)(this.MatchETag)) {
      headerOptions['x-amz-copy-source-if-match'] = this.MatchETag;
    }
    if (!(0, _helper.isEmpty)(this.NoMatchETag)) {
      headerOptions['x-amz-copy-source-if-none-match'] = this.NoMatchETag;
    }
    if (!(0, _helper.isEmpty)(this.MatchModifiedSince)) {
      headerOptions['x-amz-copy-source-if-modified-since'] = this.MatchModifiedSince;
    }
    if (!(0, _helper.isEmpty)(this.MatchUnmodifiedSince)) {
      headerOptions['x-amz-copy-source-if-unmodified-since'] = this.MatchUnmodifiedSince;
    }
    return headerOptions;
  }
}

/**
 * @deprecated use nodejs fs module
 */
exports.CopySourceOptions = CopySourceOptions;
function removeDirAndFiles(dirPath, removeSelf = true) {
  if (removeSelf) {
    return fs.rmSync(dirPath, {
      recursive: true,
      force: true
    });
  }
  fs.readdirSync(dirPath).forEach(item => {
    fs.rmSync(path.join(dirPath, item), {
      recursive: true,
      force: true
    });
  });
}
class CopyDestinationOptions {
  constructor({
    Bucket,
    Object,
    Encryption,
    UserMetadata,
    UserTags,
    LegalHold,
    RetainUntilDate,
    Mode,
    MetadataDirective,
    Headers
  }) {
    this.Bucket = Bucket;
    this.Object = Object;
    this.Encryption = Encryption ?? undefined; // null input will become undefined, easy for runtime assert
    this.UserMetadata = UserMetadata;
    this.UserTags = UserTags;
    this.LegalHold = LegalHold;
    this.Mode = Mode; // retention mode
    this.RetainUntilDate = RetainUntilDate;
    this.MetadataDirective = MetadataDirective;
    this.Headers = Headers;
  }
  getHeaders() {
    const replaceDirective = 'REPLACE';
    const headerOptions = {};
    const userTags = this.UserTags;
    if (!(0, _helper.isEmpty)(userTags)) {
      headerOptions['X-Amz-Tagging-Directive'] = replaceDirective;
      headerOptions['X-Amz-Tagging'] = (0, _helper.isObject)(userTags) ? querystring.stringify(userTags) : (0, _helper.isString)(userTags) ? userTags : '';
    }
    if (this.Mode) {
      headerOptions['X-Amz-Object-Lock-Mode'] = this.Mode; // GOVERNANCE or COMPLIANCE
    }

    if (this.RetainUntilDate) {
      headerOptions['X-Amz-Object-Lock-Retain-Until-Date'] = this.RetainUntilDate; // needs to be UTC.
    }

    if (this.LegalHold) {
      headerOptions['X-Amz-Object-Lock-Legal-Hold'] = this.LegalHold; // ON or OFF
    }

    if (this.UserMetadata) {
      for (const [key, value] of Object.entries(this.UserMetadata)) {
        headerOptions[`X-Amz-Meta-${key}`] = value.toString();
      }
    }
    if (this.MetadataDirective) {
      headerOptions[`X-Amz-Metadata-Directive`] = this.MetadataDirective;
    }
    if (this.Encryption) {
      const encryptionHeaders = (0, _helper.getEncryptionHeaders)(this.Encryption);
      for (const [key, value] of Object.entries(encryptionHeaders)) {
        headerOptions[key] = value;
      }
    }
    if (this.Headers) {
      for (const [key, value] of Object.entries(this.Headers)) {
        headerOptions[key] = value;
      }
    }
    return headerOptions;
  }
  validate() {
    if (!(0, _helper.isValidBucketName)(this.Bucket)) {
      throw new errors.InvalidBucketNameError('Invalid Destination bucket name: ' + this.Bucket);
    }
    if (!(0, _helper.isValidObjectName)(this.Object)) {
      throw new errors.InvalidObjectNameError(`Invalid Destination object name: ${this.Object}`);
    }
    if (!(0, _helper.isEmpty)(this.UserMetadata) && !(0, _helper.isObject)(this.UserMetadata)) {
      throw new errors.InvalidObjectNameError(`Destination UserMetadata should be an object with key value pairs`);
    }
    if (!(0, _helper.isEmpty)(this.Mode) && ![_type.RETENTION_MODES.GOVERNANCE, _type.RETENTION_MODES.COMPLIANCE].includes(this.Mode)) {
      throw new errors.InvalidObjectNameError(`Invalid Mode specified for destination object it should be one of [GOVERNANCE,COMPLIANCE]`);
    }
    if (this.Encryption !== undefined && (0, _helper.isEmptyObject)(this.Encryption)) {
      throw new errors.InvalidObjectNameError(`Invalid Encryption configuration for destination object `);
    }
    return true;
  }
}

/**
 * maybe this should be a generic type for Records, leave it for later refactor
 */
exports.CopyDestinationOptions = CopyDestinationOptions;
class SelectResults {
  constructor({
    records,
    // parsed data as stream
    response,
    // original response stream
    stats,
    // stats as xml
    progress // stats as xml
  }) {
    this.records = records;
    this.response = response;
    this.stats = stats;
    this.progress = progress;
  }
  setStats(stats) {
    this.stats = stats;
  }
  getStats() {
    return this.stats;
  }
  setProgress(progress) {
    this.progress = progress;
  }
  getProgress() {
    return this.progress;
  }
  setResponse(response) {
    this.response = response;
  }
  getResponse() {
    return this.response;
  }
  setRecords(records) {
    this.records = records;
  }
  getRecords() {
    return this.records;
  }
}
exports.SelectResults = SelectResults;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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