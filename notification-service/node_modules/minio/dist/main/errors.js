"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
/*
 * MinIO Javascript Library for Amazon S3 Compatible Cloud Storage, (C) 2015 MinIO, Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/// <reference lib="ES2022.Error" />

class ExtendableError extends Error {
  constructor(message, opt) {
    // error Option {cause?: unknown} is a 'nice to have',
    // don't use it internally
    super(message, opt);
    // set error name, otherwise it's always 'Error'
    this.name = this.constructor.name;
  }
}

/**
 * AnonymousRequestError is generated for anonymous keys on specific
 * APIs. NOTE: PresignedURL generation always requires access keys.
 */
class AnonymousRequestError extends ExtendableError {}

/**
 * InvalidArgumentError is generated for all invalid arguments.
 */
exports.AnonymousRequestError = AnonymousRequestError;
class InvalidArgumentError extends ExtendableError {}

/**
 * InvalidPortError is generated when a non integer value is provided
 * for ports.
 */
exports.InvalidArgumentError = InvalidArgumentError;
class InvalidPortError extends ExtendableError {}

/**
 * InvalidEndpointError is generated when an invalid end point value is
 * provided which does not follow domain standards.
 */
exports.InvalidPortError = InvalidPortError;
class InvalidEndpointError extends ExtendableError {}

/**
 * InvalidBucketNameError is generated when an invalid bucket name is
 * provided which does not follow AWS S3 specifications.
 * http://docs.aws.amazon.com/AmazonS3/latest/dev/BucketRestrictions.html
 */
exports.InvalidEndpointError = InvalidEndpointError;
class InvalidBucketNameError extends ExtendableError {}

/**
 * InvalidObjectNameError is generated when an invalid object name is
 * provided which does not follow AWS S3 specifications.
 * http://docs.aws.amazon.com/AmazonS3/latest/dev/UsingMetadata.html
 */
exports.InvalidBucketNameError = InvalidBucketNameError;
class InvalidObjectNameError extends ExtendableError {}

/**
 * AccessKeyRequiredError generated by signature methods when access
 * key is not found.
 */
exports.InvalidObjectNameError = InvalidObjectNameError;
class AccessKeyRequiredError extends ExtendableError {}

/**
 * SecretKeyRequiredError generated by signature methods when secret
 * key is not found.
 */
exports.AccessKeyRequiredError = AccessKeyRequiredError;
class SecretKeyRequiredError extends ExtendableError {}

/**
 * ExpiresParamError generated when expires parameter value is not
 * well within stipulated limits.
 */
exports.SecretKeyRequiredError = SecretKeyRequiredError;
class ExpiresParamError extends ExtendableError {}

/**
 * InvalidDateError generated when invalid date is found.
 */
exports.ExpiresParamError = ExpiresParamError;
class InvalidDateError extends ExtendableError {}

/**
 * InvalidPrefixError generated when object prefix provided is invalid
 * or does not conform to AWS S3 object key restrictions.
 */
exports.InvalidDateError = InvalidDateError;
class InvalidPrefixError extends ExtendableError {}

/**
 * InvalidBucketPolicyError generated when the given bucket policy is invalid.
 */
exports.InvalidPrefixError = InvalidPrefixError;
class InvalidBucketPolicyError extends ExtendableError {}

/**
 * IncorrectSizeError generated when total data read mismatches with
 * the input size.
 */
exports.InvalidBucketPolicyError = InvalidBucketPolicyError;
class IncorrectSizeError extends ExtendableError {}

/**
 * InvalidXMLError generated when an unknown XML is found.
 */
exports.IncorrectSizeError = IncorrectSizeError;
class InvalidXMLError extends ExtendableError {}

/**
 * S3Error is generated for errors returned from S3 server.
 * see getErrorTransformer for details
 */
exports.InvalidXMLError = InvalidXMLError;
class S3Error extends ExtendableError {}
exports.S3Error = S3Error;
class IsValidBucketNameError extends ExtendableError {}
exports.IsValidBucketNameError = IsValidBucketNameError;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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