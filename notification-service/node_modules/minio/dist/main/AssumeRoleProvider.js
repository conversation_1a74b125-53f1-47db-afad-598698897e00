"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
var http = _interopRequireWildcard(require("http"), true);
var https = _interopRequireWildcard(require("https"), true);
var _url = require("url");
var _CredentialProvider = require("./CredentialProvider.js");
var _Credentials = require("./Credentials.js");
var _helper = require("./internal/helper.js");
var _request = require("./internal/request.js");
var _response = require("./internal/response.js");
var _signing = require("./signing.js");
function _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== "function") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function (nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }
function _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || typeof obj !== "object" && typeof obj !== "function") { return { default: obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }
/**
 * @see https://docs.aws.amazon.com/STS/latest/APIReference/API_AssumeRole.html
 */

const defaultExpirySeconds = 900;
class AssumeRoleProvider extends _CredentialProvider.CredentialProvider {
  accessExpiresAt = '';
  constructor({
    stsEndpoint,
    accessKey,
    secretKey,
    durationSeconds = defaultExpirySeconds,
    sessionToken,
    policy,
    region = '',
    roleArn,
    roleSessionName,
    externalId,
    token,
    webIdentityToken,
    action = 'AssumeRole',
    transportAgent = undefined
  }) {
    super({
      accessKey,
      secretKey,
      sessionToken
    });
    this.stsEndpoint = new _url.URL(stsEndpoint);
    this.accessKey = accessKey;
    this.secretKey = secretKey;
    this.policy = policy;
    this.region = region;
    this.roleArn = roleArn;
    this.roleSessionName = roleSessionName;
    this.externalId = externalId;
    this.token = token;
    this.webIdentityToken = webIdentityToken;
    this.action = action;
    this.durationSeconds = parseInt(durationSeconds);
    let expirySeconds = this.durationSeconds;
    if (this.durationSeconds < defaultExpirySeconds) {
      expirySeconds = defaultExpirySeconds;
    }
    this.expirySeconds = expirySeconds; // for calculating refresh of credentials.

    // By default, nodejs uses a global agent if the 'agent' property
    // is set to undefined. Otherwise, it's okay to assume the users
    // know what they're doing if they specify a custom transport agent.
    this.transportAgent = transportAgent;
    const isHttp = this.stsEndpoint.protocol === 'http:';
    this.transport = isHttp ? http : https;

    /**
     * Internal Tracking variables
     */
    this._credentials = null;
  }
  getRequestConfig() {
    const hostValue = this.stsEndpoint.hostname;
    const portValue = this.stsEndpoint.port;
    const qryParams = new _url.URLSearchParams({
      Action: this.action,
      Version: '2011-06-15'
    });
    qryParams.set('DurationSeconds', this.expirySeconds.toString());
    if (this.policy) {
      qryParams.set('Policy', this.policy);
    }
    if (this.roleArn) {
      qryParams.set('RoleArn', this.roleArn);
    }
    if (this.roleSessionName != null) {
      qryParams.set('RoleSessionName', this.roleSessionName);
    }
    if (this.token != null) {
      qryParams.set('Token', this.token);
    }
    if (this.webIdentityToken) {
      qryParams.set('WebIdentityToken', this.webIdentityToken);
    }
    if (this.externalId) {
      qryParams.set('ExternalId', this.externalId);
    }
    const urlParams = qryParams.toString();
    const contentSha256 = (0, _helper.toSha256)(urlParams);
    const date = new Date();
    const requestOptions = {
      hostname: hostValue,
      port: portValue,
      path: '/',
      protocol: this.stsEndpoint.protocol,
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'content-length': urlParams.length.toString(),
        host: hostValue,
        'x-amz-date': (0, _helper.makeDateLong)(date),
        'x-amz-content-sha256': contentSha256
      },
      agent: this.transportAgent
    };
    requestOptions.headers.authorization = (0, _signing.signV4ByServiceName)(requestOptions, this.accessKey, this.secretKey, this.region, date, contentSha256, 'sts');
    return {
      requestOptions,
      requestData: urlParams
    };
  }
  async performRequest() {
    const {
      requestOptions,
      requestData
    } = this.getRequestConfig();
    const res = await (0, _request.request)(this.transport, requestOptions, requestData);
    const body = await (0, _response.readAsString)(res);
    return (0, _helper.parseXml)(body);
  }
  parseCredentials(respObj) {
    if (respObj.ErrorResponse) {
      var _respObj$ErrorRespons, _respObj$ErrorRespons2, _respObj$ErrorRespons3, _respObj$ErrorRespons4;
      throw new Error(`Unable to obtain credentials: ${(_respObj$ErrorRespons = respObj.ErrorResponse) === null || _respObj$ErrorRespons === void 0 ? void 0 : (_respObj$ErrorRespons2 = _respObj$ErrorRespons.Error) === null || _respObj$ErrorRespons2 === void 0 ? void 0 : _respObj$ErrorRespons2.Code} ${(_respObj$ErrorRespons3 = respObj.ErrorResponse) === null || _respObj$ErrorRespons3 === void 0 ? void 0 : (_respObj$ErrorRespons4 = _respObj$ErrorRespons3.Error) === null || _respObj$ErrorRespons4 === void 0 ? void 0 : _respObj$ErrorRespons4.Message}`, {
        cause: respObj
      });
    }
    const {
      AssumeRoleResponse: {
        AssumeRoleResult: {
          Credentials: {
            AccessKeyId: accessKey,
            SecretAccessKey: secretKey,
            SessionToken: sessionToken,
            Expiration: expiresAt
          }
        }
      }
    } = respObj;
    this.accessExpiresAt = expiresAt;
    return new _Credentials.Credentials({
      accessKey,
      secretKey,
      sessionToken
    });
  }
  async refreshCredentials() {
    try {
      const assumeRoleCredentials = await this.performRequest();
      this._credentials = this.parseCredentials(assumeRoleCredentials);
    } catch (err) {
      throw new Error(`Failed to get Credentials: ${err}`, {
        cause: err
      });
    }
    return this._credentials;
  }
  async getCredentials() {
    if (this._credentials && !this.isAboutToExpire()) {
      return this._credentials;
    }
    this._credentials = await this.refreshCredentials();
    return this._credentials;
  }
  isAboutToExpire() {
    const expiresAt = new Date(this.accessExpiresAt);
    const provisionalExpiry = new Date(Date.now() + 1000 * 10); // check before 10 seconds.
    return provisionalExpiry > expiresAt;
  }
}

// deprecated default export, please use named exports.
// keep for backward compatibility.
// eslint-disable-next-line import/no-default-export
exports.AssumeRoleProvider = AssumeRoleProvider;
var _default = AssumeRoleProvider;
exports.default = _default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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