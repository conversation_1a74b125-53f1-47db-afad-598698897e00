{"name": "stream-chain", "version": "2.2.5", "description": "Chain functions as transform streams.", "main": "index.js", "scripts": {"debug": "node --inspect-brk tests/tests.js", "test": "node tests/tests.js"}, "repository": {"type": "git", "url": "git+https://github.com/uhop/stream-chain.git"}, "keywords": ["stream", "chain"], "author": "<PERSON> <<EMAIL>> (http://lazutkin.com/)", "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/uhop/stream-chain/issues"}, "homepage": "https://github.com/uhop/stream-chain#readme", "devDependencies": {"heya-unit": "^0.3.0"}, "files": ["/*.js", "/utils"]}