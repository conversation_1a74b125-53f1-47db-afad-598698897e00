import { useState, useEffect, useMemo } from 'react';
import axios from 'axios';
import { useAuth } from '../hooks/useAuth';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { Chart } from './ui/chart';
import { Alert, AlertDescription } from './ui/alert';
import { Badge } from './ui/badge';
import { Button } from './ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from './ui/tabs';
import {
  MaterialReactTable,
  useMaterialReactTable,
  type MRT_ColumnDef,
} from 'material-react-table';
import {
  Bell,
  Mail,
  MessageCircle,
  Phone,
  TrendingUp,
  TrendingDown,
  Clock,
  CheckCircle,
  XCircle,
  RefreshCw
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface NotificationJob {
  id: string;
  type: 'email' | 'telegram' | 'whatsapp';
  recipient: string;
  subject?: string;
  status: 'queued' | 'sent' | 'failed';
  createdAt: string;
  sentAt?: string;
  errorMessage?: string;
}

interface DashboardStats {
  totalJobs: number;
  successRate: number;
  failureRate: number;
  queuedJobs: number;
  recentJobs: NotificationJob[];
}

export default function Dashboard() {
  const [stats, setStats] = useState<DashboardStats>({
    totalJobs: 0,
    successRate: 0,
    failureRate: 0,
    queuedJobs: 0,
    recentJobs: []
  });
  const [jobs, setJobs] = useState<NotificationJob[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const { authToken } = useAuth();
  const { toast } = useToast();

  const fetchData = async () => {
    setLoading(true);
    try {
      const [statsRes, jobsRes] = await Promise.all([
        axios.get('/api/user-stats', {
          headers: { Authorization: `Bearer ${authToken}` }
        }),
        axios.get('/api/jobs', {
          headers: { Authorization: `Bearer ${authToken}` }
        })
      ]);

      setStats(statsRes.data);
      setJobs(jobsRes.data);
      setError('');
    } catch (err) {
      setError('Failed to fetch data');
      toast({
        title: "Error",
        description: "Failed to fetch dashboard data",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [authToken]);

  // Material React Table columns
  const columns = useMemo<MRT_ColumnDef<NotificationJob>[]>(
    () => [
      {
        accessorKey: 'id',
        header: 'Job ID',
        size: 100,
      },
      {
        accessorKey: 'type',
        header: 'Type',
        size: 100,
        Cell: ({ cell }) => {
          const type = cell.getValue<string>();
          const icons = {
            email: <Mail className="h-4 w-4" />,
            telegram: <MessageCircle className="h-4 w-4" />,
            whatsapp: <Phone className="h-4 w-4" />
          };
          return (
            <div className="flex items-center gap-2">
              {icons[type as keyof typeof icons]}
              <span className="capitalize">{type}</span>
            </div>
          );
        },
      },
      {
        accessorKey: 'recipient',
        header: 'Recipient',
        size: 200,
      },
      {
        accessorKey: 'subject',
        header: 'Subject',
        size: 200,
        Cell: ({ cell }) => {
          const subject = cell.getValue<string>();
          return subject || '-';
        },
      },
      {
        accessorKey: 'status',
        header: 'Status',
        size: 120,
        Cell: ({ cell }) => {
          const status = cell.getValue<string>();
          const variants = {
            queued: { variant: 'secondary' as const, icon: <Clock className="h-3 w-3" /> },
            sent: { variant: 'default' as const, icon: <CheckCircle className="h-3 w-3" /> },
            failed: { variant: 'destructive' as const, icon: <XCircle className="h-3 w-3" /> }
          };
          const config = variants[status as keyof typeof variants];
          return (
            <Badge variant={config.variant} className="flex items-center gap-1">
              {config.icon}
              <span className="capitalize">{status}</span>
            </Badge>
          );
        },
      },
      {
        accessorKey: 'createdAt',
        header: 'Created',
        size: 150,
        Cell: ({ cell }) => {
          const date = new Date(cell.getValue<string>());
          return date.toLocaleString();
        },
      },
      {
        accessorKey: 'sentAt',
        header: 'Sent At',
        size: 150,
        Cell: ({ cell }) => {
          const date = cell.getValue<string>();
          return date ? new Date(date).toLocaleString() : '-';
        },
      },
    ],
    []
  );

  const table = useMaterialReactTable({
    columns,
    data: jobs,
    enableRowSelection: false,
    enableColumnOrdering: true,
    enableGlobalFilter: true,
    enableSorting: true,
    enablePagination: true,
    initialState: {
      pagination: { pageSize: 10, pageIndex: 0 },
      sorting: [{ id: 'createdAt', desc: true }],
    },
    muiTableContainerProps: {
      sx: { maxHeight: '600px' },
    },
  });

  // Calculate chart data
  const jobsByType = jobs.reduce((acc, job) => {
    acc[job.type] = (acc[job.type] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  const jobsByStatus = jobs.reduce((acc, job) => {
    acc[job.status] = (acc[job.status] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
          <p className="text-muted-foreground">
            Monitor your notification jobs and system performance
          </p>
        </div>
        <Button onClick={fetchData} disabled={loading} className="flex items-center gap-2">
          <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Jobs</CardTitle>
            <Bell className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalJobs}</div>
            <p className="text-xs text-muted-foreground">
              All notification jobs
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.successRate}%</div>
            <p className="text-xs text-muted-foreground">
              Successfully delivered
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Failure Rate</CardTitle>
            <TrendingDown className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.failureRate}%</div>
            <p className="text-xs text-muted-foreground">
              Failed deliveries
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Queued Jobs</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.queuedJobs}</div>
            <p className="text-xs text-muted-foreground">
              Pending delivery
            </p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="jobs">All Jobs</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Jobs by Status</CardTitle>
              </CardHeader>
              <CardContent>
                <Chart
                  type="pie"
                  data={{
                    labels: Object.keys(jobsByStatus),
                    datasets: [{
                      label: 'Jobs by Status',
                      data: Object.values(jobsByStatus),
                      backgroundColor: ['#36A2EB', '#4BC0C0', '#FF6384'],
                      borderColor: ['#ffffff', '#ffffff', '#ffffff'],
                      borderWidth: 2
                    }]
                  }}
                  options={{
                    responsive: true,
                    plugins: {
                      legend: {
                        position: 'bottom',
                      }
                    }
                  }}
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Jobs by Type</CardTitle>
              </CardHeader>
              <CardContent>
                <Chart
                  type="bar"
                  data={{
                    labels: Object.keys(jobsByType),
                    datasets: [{
                      label: 'Jobs by Type',
                      data: Object.values(jobsByType),
                      backgroundColor: ['#8884d8', '#82ca9d', '#ffc658'],
                      borderColor: ['#8884d8', '#82ca9d', '#ffc658'],
                      borderWidth: 1
                    }]
                  }}
                  options={{
                    responsive: true,
                    plugins: {
                      legend: {
                        display: false,
                      }
                    },
                    scales: {
                      y: {
                        beginAtZero: true,
                      }
                    }
                  }}
                />
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="jobs">
          <Card>
            <CardHeader>
              <CardTitle>All Notification Jobs</CardTitle>
            </CardHeader>
            <CardContent>
              <MaterialReactTable table={table} />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}